import { Component, Inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CommonModule } from '@angular/common';
import { 
  CoinDenomination, 
  COIN_DENOMINATION_LABELS, 
  COIN_BATCH_CONFIG, 
  COIN_BATCH_VALUES,
  TransactionType 
} from '../../../../shared/models/inventory.model';

export interface AddCoinDialogData {
  denomination: CoinDenomination;
  currentQuantity: number;
  currentBatches: number;
}

export interface AddCoinResult {
  denomination: CoinDenomination;
  quantity: number;
  batches: number;
  action: 'add' | 'remove';
  notes?: string;
  transactionType: TransactionType;
}

@Component({
  selector: 'app-add-coin-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './add-coin-modal.component.html',
  styleUrls: ['./add-coin-modal.component.scss']
})
export class AddCoinModalComponent {
  addCoinForm: FormGroup;
  isSubmitting = false;
  quantityType: 'batches' | 'singles' = 'batches';
  
  // Quick amounts for batches and singles
  batchQuickAmounts = [1, 2, 5, 10, 20];
  singlesQuickAmounts: number[] = [];

  // Coin configuration
  coinBatchConfig = COIN_BATCH_CONFIG;
  coinBatchValues = COIN_BATCH_VALUES;
  coinLabels = COIN_DENOMINATION_LABELS;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AddCoinModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCoinDialogData
  ) {
    this.addCoinForm = this.fb.group({
      action: ['add', Validators.required],
      batchQuantity: [1, [Validators.required, Validators.min(1)]],
      singlesQuantity: [1, [Validators.required, Validators.min(1)]],
      notes: ['', [Validators.maxLength(500)]]
    });

    // Set singles quick amounts based on coin denomination
    this.setSinglesQuickAmounts();
  }

  private setSinglesQuickAmounts(): void {
    const coinsPerBatch = this.coinBatchConfig[this.data.denomination];
    this.singlesQuickAmounts = [
      Math.floor(coinsPerBatch * 0.25),
      Math.floor(coinsPerBatch * 0.5),
      coinsPerBatch,
      coinsPerBatch * 2,
      coinsPerBatch * 5
    ].filter(amount => amount > 0);
  }

  onSubmit(): void {
    if (this.addCoinForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      setTimeout(() => {
        const totalCoins = this.getTotalCoins();
        const totalBatches = this.getTotalBatches();
        
        const result: AddCoinResult = {
          denomination: this.data.denomination,
          quantity: totalCoins,
          batches: totalBatches,
          action: this.addCoinForm.get('action')?.value,
          notes: this.addCoinForm.get('notes')?.value,
          transactionType: this.addCoinForm.get('action')?.value === 'add' ? TransactionType.ADD : TransactionType.REMOVE
        };
        this.dialogRef.close(result);
      }, 800);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  selectAction(action: 'add' | 'remove'): void {
    this.addCoinForm.patchValue({ action });
  }

  setQuantityType(type: 'batches' | 'singles'): void {
    this.quantityType = type;
  }

  getQuantityValue(): number {
    const controlName = this.quantityType === 'batches' ? 'batchQuantity' : 'singlesQuantity';
    return this.addCoinForm.get(controlName)?.value || 1;
  }

  setQuantityValue(amount: number): void {
    const controlName = this.quantityType === 'batches' ? 'batchQuantity' : 'singlesQuantity';
    this.addCoinForm.patchValue({ [controlName]: amount });
  }

  adjustQuantity(delta: number): void {
    const currentValue = this.getQuantityValue();
    const newValue = Math.max(1, currentValue + delta);
    this.setQuantityValue(newValue);
  }

  onQuantityInputChange(event: any): void {
    const value = parseInt(event.target.value) || 1;
    const validValue = Math.max(1, value);
    this.setQuantityValue(validValue);
    
    if (value !== validValue) {
      event.target.value = validValue;
    }
  }

  getQuickAmounts(): number[] {
    return this.quantityType === 'batches' ? this.batchQuickAmounts : this.singlesQuickAmounts;
  }

  getTotalCoins(): number {
    const batchQuantity = this.addCoinForm.get('batchQuantity')?.value || 0;
    const singlesQuantity = this.addCoinForm.get('singlesQuantity')?.value || 0;
    const coinsPerBatch = this.coinBatchConfig[this.data.denomination];
    
    if (this.quantityType === 'batches') {
      return batchQuantity * coinsPerBatch;
    } else {
      return singlesQuantity;
    }
  }

  getTotalBatches(): number {
    const totalCoins = this.getTotalCoins();
    const coinsPerBatch = this.coinBatchConfig[this.data.denomination];
    return Math.ceil(totalCoins / coinsPerBatch);
  }

  getNewTotalCoins(): number {
    const totalCoins = this.getTotalCoins();
    const action = this.addCoinForm.get('action')?.value;
    
    if (action === 'add') {
      return this.data.currentQuantity + totalCoins;
    } else {
      return Math.max(0, this.data.currentQuantity - totalCoins);
    }
  }

  getNewTotalBatches(): number {
    const newTotalCoins = this.getNewTotalCoins();
    const coinsPerBatch = this.coinBatchConfig[this.data.denomination];
    return Math.ceil(newTotalCoins / coinsPerBatch);
  }

  getTotalValue(): number {
    return this.getNewTotalCoins() * this.data.denomination;
  }

  getCoinsPerBatch(): number {
    return this.coinBatchConfig[this.data.denomination];
  }

  getBatchValue(): number {
    return this.coinBatchValues[this.data.denomination];
  }

  getDenominationLabel(): string {
    return this.coinLabels[this.data.denomination];
  }

  formatCurrency(value: number): string {
    if (value >= 1) {
      return `R${value.toFixed(0)}`;
    } else {
      return `${Math.round(value * 100)}c`;
    }
  }
}
