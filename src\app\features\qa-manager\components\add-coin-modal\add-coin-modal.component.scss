// Absa Brand Colors
:root {
  --absa-red: #e60000;
  --absa-red-dark: #cc0000;
  --absa-red-light: rgba(230, 0, 0, 0.1);
  --absa-success: #4caf50;
  --absa-success-light: rgba(76, 175, 80, 0.1);
  --absa-warning: #ff9800;
  --absa-warning-light: rgba(255, 152, 0, 0.1);
  --absa-text: #333333;
  --absa-text-light: #666666;
  --absa-text-muted: #999999;
  --absa-bg: #ffffff;
  --absa-bg-light: #f5f5f5;
  --absa-border: #e0e0e0;
  --absa-border-light: #eeeeee;
  --absa-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --absa-radius: 8px;
  --absa-transition: all 0.3s ease;
  --coin-gold: #ffd700;
  --coin-silver: #c0c0c0;
  --coin-bronze: #cd7f32;
}

// Modal Container
.modal-container {
  width: 600px;
  max-width: 95vw;
  background: var(--absa-bg);
  border-radius: var(--absa-radius);
  overflow: hidden;
}

// Header
.modal-header {
  position: relative;
  background: linear-gradient(135deg, var(--coin-gold) 0%, #e6c200 100%);
  color: #333;
  padding: 24px;
  margin: -24px -24px 0 -24px;

  .header-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .denomination-badge {
    &.coin-badge {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      width: 70px;
      height: 70px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      border: 3px solid rgba(255, 255, 255, 0.5);

      .currency-value {
        font-size: 16px;
        font-weight: 800;
        color: var(--absa-text);
      }

      .coin-icon {
        mat-icon {
          font-size: 20px;
          color: var(--coin-gold);
        }
      }
    }
  }

  .header-info {
    flex: 1;

    .modal-title {
      margin: 0 0 4px 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .current-stock,
    .batch-info {
      margin: 0 0 4px 0;
      font-size: 13px;
      opacity: 0.9;
      display: flex;
      align-items: center;
      gap: 4px;
      color: #333;

      mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }
  }

  .close-button {
    position: absolute;
    top: 16px;
    right: 16px;
    color: #333;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }
  }
}

// Content
.modal-content {
  padding: 24px 0;
  max-height: 60vh;
  overflow-y: auto;
}

// Form Sections
.form-section {
  margin-bottom: 24px;

  .section-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;

    .section-icon {
      color: var(--coin-gold);
      font-size: 20px;
    }

    .section-label {
      font-weight: 600;
      color: var(--absa-text);
      font-size: 16px;

      .optional {
        color: var(--absa-text-muted);
        font-weight: 400;
        font-size: 14px;
      }
    }
  }
}

.full-width {
  width: 100%;
}

// Action Cards
.action-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .action-card {
    background: var(--absa-bg);
    border: 2px solid var(--absa-border);
    border-radius: var(--absa-radius);
    padding: 16px;
    cursor: pointer;
    transition: var(--absa-transition);
    text-align: center;

    &:hover {
      border-color: var(--coin-gold);
      box-shadow: var(--absa-shadow);
    }

    &.selected {
      border-color: var(--coin-gold);
      background: rgba(255, 215, 0, 0.1);
      box-shadow: var(--absa-shadow);
    }

    .card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin: 0 auto 12px;

      &.add-icon {
        background: var(--absa-success-light);

        mat-icon {
          color: var(--absa-success);
          font-size: 24px;
        }
      }

      &.remove-icon {
        background: var(--absa-warning-light);

        mat-icon {
          color: var(--absa-warning);
          font-size: 24px;
        }
      }
    }

    .card-content {
      h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--absa-text);
      }

      p {
        margin: 0;
        font-size: 12px;
        color: var(--absa-text-light);
      }
    }
  }
}

// Quantity Type Selector
.quantity-type-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;

  .type-btn {
    background: var(--absa-bg);
    border: 2px solid var(--absa-border);
    border-radius: var(--absa-radius);
    padding: 16px 12px;
    cursor: pointer;
    transition: var(--absa-transition);
    text-align: center;

    &:hover {
      border-color: var(--coin-gold);
      box-shadow: var(--absa-shadow);
    }

    &.active {
      border-color: var(--coin-gold);
      background: rgba(255, 215, 0, 0.1);
      box-shadow: var(--absa-shadow);
    }

    mat-icon {
      display: block;
      margin: 0 auto 8px;
      font-size: 24px;
      color: var(--coin-gold);
    }

    span {
      display: block;
      font-weight: 600;
      color: var(--absa-text);
      margin-bottom: 4px;
    }

    small {
      display: block;
      font-size: 11px;
      color: var(--absa-text-light);
    }
  }
}

// Quantity Controls
.quantity-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;

  .quantity-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--absa-transition);

    &.decrease {
      background: var(--absa-warning);
      color: white;

      &:hover:not(:disabled) {
        background: var(--absa-warning);
        transform: scale(1.1);
      }

      &:disabled {
        background: var(--absa-border);
        color: var(--absa-text-muted);
        cursor: not-allowed;
      }
    }

    &.increase {
      background: var(--absa-success);
      color: white;

      &:hover {
        background: var(--absa-success);
        transform: scale(1.1);
      }
    }

    mat-icon {
      font-size: 20px;
    }
  }

  .quantity-display {
    text-align: center;

    .quantity-input {
      width: 80px;
      height: 50px;
      border: 2px solid var(--absa-border);
      border-radius: var(--absa-radius);
      background: var(--absa-bg);
      font-size: 20px;
      font-weight: 600;
      text-align: center;
      color: var(--absa-text);
      transition: var(--absa-transition);

      &:focus {
        outline: none;
        border-color: var(--coin-gold);
      }

      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type=number] {
        -moz-appearance: textfield;
      }
    }

    .quantity-label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: var(--absa-text-light);
      margin-top: 4px;
    }
  }
}

// Quick Select
.quick-select {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 16px;

  .quick-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--absa-text-light);
    margin-right: 8px;
  }

  .quick-btn {
    padding: 6px 12px;
    border: 1px solid var(--absa-border);
    border-radius: var(--absa-radius);
    background: var(--absa-bg);
    color: var(--absa-text);
    font-weight: 500;
    cursor: pointer;
    transition: var(--absa-transition);
    font-size: 14px;

    &:hover {
      border-color: var(--coin-gold);
      background: rgba(255, 215, 0, 0.1);
    }

    &.selected {
      border-color: var(--coin-gold);
      background: var(--coin-gold);
      color: #333;
    }
  }
}

// Conversion Display
.conversion-display {
  background: var(--absa-bg-light);
  border-radius: var(--absa-radius);
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 12px;

  .conversion-item {
    text-align: center;

    .conversion-label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: var(--absa-text-light);
      margin-bottom: 4px;
    }

    .conversion-value {
      display: block;
      font-size: 16px;
      font-weight: 700;
      color: var(--coin-gold);
    }
  }
}

// Summary Cards
.summary-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;

  .summary-card {
    background: var(--absa-bg);
    border: 1px solid var(--absa-border);
    border-radius: var(--absa-radius);
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;

    &.current-stock {
      .summary-icon {
        background: var(--absa-bg-light);
        color: var(--absa-text);
      }
    }

    &.change-amount {
      &.adding {
        border-color: var(--absa-success);
        background: var(--absa-success-light);

        .summary-icon {
          background: var(--absa-success);
          color: white;
        }
      }

      &.removing {
        border-color: var(--absa-warning);
        background: var(--absa-warning-light);

        .summary-icon {
          background: var(--absa-warning);
          color: white;
        }
      }
    }

    &.new-total {
      border-color: var(--coin-gold);
      background: rgba(255, 215, 0, 0.1);

      .summary-icon {
        background: var(--coin-gold);
        color: #333;
      }
    }

    &.total-value {
      grid-column: 1 / -1;
      border-color: var(--coin-gold);
      background: rgba(255, 215, 0, 0.1);

      .summary-icon {
        background: var(--coin-gold);
        color: #333;
      }

      .summary-value.currency {
        font-size: 18px;
        font-weight: 700;
        color: var(--coin-gold);
      }
    }

    .summary-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        font-size: 20px;
      }
    }

    .summary-content {
      flex: 1;

      .summary-label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: var(--absa-text-light);
        margin-bottom: 2px;
      }

      .summary-value {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: var(--absa-text);
      }
    }
  }
}

// Actions
.modal-actions {
  padding: 16px 0 0 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid var(--absa-border);
  margin-top: 24px;

  .cancel-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--absa-text-light);

    &:hover {
      background-color: var(--absa-bg-light);
    }
  }

  .submit-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-spinner {
      position: absolute;
      right: 8px;
    }
  }
}

// Global Modal Styles
::ng-deep {
  // Backdrop blur effect
  .add-coin-modal-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.5) !important;
  }

  // Modern modal panel
  .add-coin-modal-panel {
    border-radius: var(--absa-radius) !important;
    overflow: hidden !important;

    .mat-mdc-dialog-container {
      border-radius: var(--absa-radius) !important;
      overflow: hidden !important;
      padding: 0 !important;
    }
  }

  // Form field styles
  .modal-container {
    .mat-mdc-form-field {
      .mat-mdc-form-field-outline {
        color: var(--absa-border) !important;
      }

      .mat-mdc-form-field-focus-overlay {
        background-color: transparent !important;
      }

      &.mat-focused {
        .mat-mdc-form-field-outline-thick {
          color: var(--coin-gold) !important;
        }
      }

      .mat-mdc-select-value,
      .mat-mdc-input-element {
        color: var(--absa-text) !important;
      }

      .mat-mdc-form-field-hint {
        color: var(--absa-text-light) !important;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .modal-container {
    width: 95vw !important;

    .action-cards,
    .quantity-type-selector,
    .summary-cards {
      grid-template-columns: 1fr;
    }

    .summary-cards .summary-card.total-value {
      grid-column: 1;
    }

    .conversion-display {
      grid-template-columns: 1fr;
      gap: 8px;
    }

    .modal-actions {
      flex-direction: column;
      gap: 8px;

      .cancel-btn,
      .submit-btn {
        width: 100%;
        justify-content: center;
      }
    }
  }
}
