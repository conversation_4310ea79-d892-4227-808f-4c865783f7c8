<div class="add-notes-modal">
  <div class="modal-header">
    <h2 mat-dialog-title>
      <mat-icon class="header-icon">note_add</mat-icon>
      {{ data.title || 'Add Notes' }}
    </h2>
    <button mat-icon-button mat-dialog-close class="close-button">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div mat-dialog-content class="modal-content">
    <form [formGroup]="addNotesForm" (ngSubmit)="onSubmit()" class="notes-form">
      
      <!-- Note Series Selection -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Note Series</mat-label>
        <mat-select formControlName="series" required>
          <mat-option *ngFor="let series of noteSeries" [value]="series.id">
            <div class="series-option">
              <div class="series-color-indicator" [style.background-color]="series.color"></div>
              <span class="series-name">{{ series.name }}</span>
            </div>
          </mat-option>
        </mat-select>
        <mat-hint>Select the note series type</mat-hint>
        <mat-error *ngIf="getFieldError('series')">
          {{ getFieldError('series') }}
        </mat-error>
      </mat-form-field>

      <!-- Denomination Selection -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Denomination</mat-label>
        <mat-select formControlName="denomination" required [disabled]="!getSelectedSeries()">
          <mat-option *ngFor="let denomination of getAvailableDenominations()" [value]="denomination">
            <div class="denomination-option">
              <mat-icon class="currency-icon">account_balance_wallet</mat-icon>
              <span class="denomination-value">R{{ denomination }}</span>
            </div>
          </mat-option>
        </mat-select>
        <mat-hint>Select the note denomination</mat-hint>
        <mat-error *ngIf="getFieldError('denomination')">
          {{ getFieldError('denomination') }}
        </mat-error>
      </mat-form-field>

      <!-- Transaction Type -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Transaction Type</mat-label>
        <mat-select formControlName="transactionType" required>
          <mat-option *ngFor="let type of transactionTypes" [value]="type">
            <div class="transaction-option">
              <mat-icon [class]="type === 'Addition' ? 'add-icon' : 'subtract-icon'">
                {{ type === 'Addition' ? 'add_circle' : 'remove_circle' }}
              </mat-icon>
              <span>{{ type }}</span>
            </div>
          </mat-option>
        </mat-select>
        <mat-hint>Choose to add or remove notes</mat-hint>
        <mat-error *ngIf="getFieldError('transactionType')">
          {{ getFieldError('transactionType') }}
        </mat-error>
      </mat-form-field>

      <!-- Quantity Input -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Quantity</mat-label>
        <input matInput type="number" formControlName="quantity" min="1" required>
        <mat-hint>Number of notes to {{ addNotesForm.get('transactionType')?.value?.toLowerCase() || 'add' }}</mat-hint>
        <mat-error *ngIf="getFieldError('quantity')">
          {{ getFieldError('quantity') }}
        </mat-error>
      </mat-form-field>

      <!-- Total Value Display -->
      <div class="total-value-card" *ngIf="getTotalValue() > 0">
        <div class="total-content">
          <div class="total-icon">
            <mat-icon>calculate</mat-icon>
          </div>
          <div class="total-details">
            <div class="total-label">Total Value</div>
            <div class="total-amount">R{{ getTotalValue() | number:'1.2-2' }}</div>
          </div>
        </div>
      </div>

      <!-- Notes -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Additional Notes (Optional)</mat-label>
        <textarea matInput formControlName="notes" rows="3" 
                  placeholder="Add any additional notes or comments..."></textarea>
        <mat-hint>Optional notes about this transaction</mat-hint>
      </mat-form-field>

    </form>
  </div>

  <div mat-dialog-actions class="modal-actions">
    <button mat-button type="button" (click)="onCancel()" class="cancel-button">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button color="primary" (click)="onSubmit()" 
            [disabled]="!addNotesForm.valid" class="submit-button">
      <mat-icon>{{ addNotesForm.get('transactionType')?.value === 'Addition' ? 'add' : 'remove' }}</mat-icon>
      {{ addNotesForm.get('transactionType')?.value === 'Addition' ? 'Add Notes' : 'Remove Notes' }}
    </button>
  </div>
</div>
