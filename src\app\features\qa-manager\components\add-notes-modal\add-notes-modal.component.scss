.add-notes-modal {
  min-width: 500px;
  max-width: 600px;
  font-family: 'Roboto', sans-serif;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 16px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -24px -24px 24px -24px;

    h2 {
      display: flex;
      align-items: center;
      gap: 12px;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;

      .header-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
      }
    }

    .close-button {
      color: white;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .modal-content {
    padding: 0 24px;
    max-height: 70vh;
    overflow-y: auto;

    .notes-form {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .full-width {
        width: 100%;
      }

      // Series option styling
      .series-option {
        display: flex;
        align-items: center;
        gap: 12px;

        .series-color-indicator {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .series-name {
          font-weight: 500;
        }
      }

      // Denomination option styling
      .denomination-option {
        display: flex;
        align-items: center;
        gap: 12px;

        .currency-icon {
          color: #4CAF50;
          font-size: 20px;
        }

        .denomination-value {
          font-weight: 500;
          font-size: 1.1rem;
        }
      }

      // Transaction option styling
      .transaction-option {
        display: flex;
        align-items: center;
        gap: 12px;

        .add-icon {
          color: #4CAF50;
        }

        .subtract-icon {
          color: #f44336;
        }

        span {
          font-weight: 500;
        }
      }

      // Total value card
      .total-value-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 20px;
        margin: 16px 0;
        color: white;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);

        .total-content {
          display: flex;
          align-items: center;
          gap: 16px;

          .total-icon {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            padding: 12px;
            display: flex;
            align-items: center;
            justify-content: center;

            mat-icon {
              font-size: 24px;
              width: 24px;
              height: 24px;
            }
          }

          .total-details {
            flex: 1;

            .total-label {
              font-size: 0.9rem;
              opacity: 0.9;
              margin-bottom: 4px;
            }

            .total-amount {
              font-size: 1.8rem;
              font-weight: 600;
              text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
          }
        }
      }
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px;
    border-top: 1px solid #e0e0e0;
    margin: 24px -24px -24px -24px;
    background-color: #fafafa;

    .cancel-button {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    .submit-button {
      display: flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);

      &:hover:not(:disabled) {
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        transform: translateY(-1px);
      }

      &:disabled {
        background: #ccc;
        box-shadow: none;
        transform: none;
      }
    }
  }
}

// Form field customizations
::ng-deep .add-notes-modal {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      border-radius: 8px;
    }

    .mat-mdc-form-field-hint-wrapper,
    .mat-mdc-form-field-error-wrapper {
      padding: 0 16px;
    }

    .mat-mdc-form-field-hint {
      color: #666;
      font-size: 0.85rem;
    }
  }

  .mat-mdc-select-panel {
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

// Animation for modal entrance
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.add-notes-modal {
  animation: modalSlideIn 0.3s ease-out;
}
