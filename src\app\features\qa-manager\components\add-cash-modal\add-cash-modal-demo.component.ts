import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { AddCashModalService, AddCashResult } from './add-cash-modal.service';

@Component({
  selector: 'app-add-cash-modal-demo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="demo-container">
      <h2>Add Cash Modal Demo</h2>
      <p>Click the buttons below to test the modal with different pre-populated values:</p>
      
      <div class="demo-buttons">
        <button mat-raised-button color="primary" (click)="openEmptyModal()">
          <mat-icon>add</mat-icon>
          Open Empty Modal
        </button>
        
        <button mat-raised-button color="accent" (click)="openPrePopulatedModal()">
          <mat-icon>auto_awesome</mat-icon>
          Mandela Series R10 (Pre-populated)
        </button>
        
        <button mat-raised-button color="warn" (click)="openBig5R100Modal()">
          <mat-icon>nature</mat-icon>
          Big 5 Series R100 (Pre-populated)
        </button>
        
        <button mat-raised-button (click)="openV6R200Modal()">
          <mat-icon>new_releases</mat-icon>
          V6 Series R200 (Pre-populated)
        </button>
      </div>
      
      <div class="demo-info">
        <h3>Features Demonstrated:</h3>
        <ul>
          <li>✨ Modern glassmorphism design with neutral colors</li>
          <li>🌫️ Enhanced background blur effect</li>
          <li>🎯 Pre-population of series and denomination</li>
          <li>🏷️ Visual indicators for pre-selected values</li>
          <li>📱 Responsive design</li>
          <li>🎨 Clean, professional interface</li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: 2rem;
      max-width: 800px;
      margin: 0 auto;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    h2 {
      color: var(--absa-dark-blue, #1e293b);
      margin-bottom: 1rem;
    }
    
    p {
      color: var(--absa-gray-medium, #64748b);
      margin-bottom: 2rem;
    }
    
    .demo-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
      
      button {
        padding: 1rem;
        font-size: 1rem;
        
        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }
    
    .demo-info {
      background: linear-gradient(135deg, 
        rgba(248, 250, 252, 0.8) 0%, 
        rgba(241, 245, 249, 0.9) 100%);
      border: 1px solid rgba(226, 232, 240, 0.6);
      border-radius: 16px;
      padding: 1.5rem;
      backdrop-filter: blur(10px);
      
      h3 {
        color: var(--absa-dark-blue, #1e293b);
        margin-bottom: 1rem;
      }
      
      ul {
        list-style: none;
        padding: 0;
        
        li {
          padding: 0.5rem 0;
          color: var(--absa-gray-dark, #374151);
          
          &:before {
            margin-right: 0.5rem;
          }
        }
      }
    }
    
    @media (max-width: 768px) {
      .demo-buttons {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class AddCashModalDemoComponent {

  constructor(
    private addCashModalService: AddCashModalService,
    private snackBar: MatSnackBar
  ) {}

  openEmptyModal(): void {
    this.addCashModalService.openAddCashModal()
      .subscribe(result => {
        this.handleResult(result, 'Empty Modal');
      });
  }

  openPrePopulatedModal(): void {
    this.addCashModalService.openAddCashModal({
      seriesName: 'Mandela Series',
      denomination: 10,
      currentQuantity: 87
    }).subscribe(result => {
      this.handleResult(result, 'Mandela R10');
    });
  }

  openBig5R100Modal(): void {
    this.addCashModalService.openAddCashModal({
      seriesName: 'Big 5 Series',
      denomination: 100,
      currentQuantity: 156
    }).subscribe(result => {
      this.handleResult(result, 'Big 5 R100');
    });
  }

  openV6R200Modal(): void {
    this.addCashModalService.openAddCashModal({
      seriesName: 'V6 Series',
      denomination: 200,
      currentQuantity: 89
    }).subscribe(result => {
      this.handleResult(result, 'V6 R200');
    });
  }

  private handleResult(result: AddCashResult | undefined, context: string): void {
    if (result?.success) {
      this.snackBar.open(
        `${context}: Successfully added ${result.added} notes!`,
        'Close',
        { 
          duration: 4000,
          panelClass: ['success-snackbar']
        }
      );
    } else if (result?.success === false) {
      this.snackBar.open(
        `${context}: Operation cancelled`,
        'Close',
        { duration: 3000 }
      );
    }
  }
}
