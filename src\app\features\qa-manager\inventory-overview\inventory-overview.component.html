<div class="absa-inventory-container">
  <!-- Hero Header Section -->
  <header class="absa-hero-header">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <mat-icon class="hero-icon">inventory</mat-icon>
          Detailed Inventory
        </h1>
        <p class="hero-subtitle">Manage cash inventory by note series</p>
      </div>
    </div>
  </header>

  <main class="absa-main-content">
    <!-- Enhanced Summary Dashboard -->
    <section class="absa-dashboard-section" *ngIf="inventorySummary" aria-label="Inventory Summary">
      <div class="dashboard-grid">
        <!-- Total Value Card -->
        <article class="absa-metric-card value-card" tabindex="0" role="button" aria-label="Total inventory value">
          <div class="card-decoration"></div>
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
              <div class="metric-trend positive" aria-label="Trending up 2.5%">
                <mat-icon>trending_up</mat-icon>
                <span>+2.5%</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value" [attr.aria-label]="'Total value: ' + formatCurrency(inventorySummary.totalValue)">
                {{ formatCurrency(inventorySummary.totalValue) }}
              </div>
              <div class="metric-label">Total Value</div>
              <div class="metric-description">Across all note series</div>
            </div>
            <div class="card-progress">
              <div class="progress-bar" style="width: 85%"></div>
            </div>
          </div>
        </article>

        <!-- Total Notes Card -->
        <article class="absa-metric-card notes-card" tabindex="0" role="button" aria-label="Total notes count">
          <div class="card-decoration"></div>
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>receipt_long</mat-icon>
              </div>
              <div class="metric-trend neutral" aria-label="No change">
                <mat-icon>remove</mat-icon>
                <span>0%</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value" [attr.aria-label]="'Total notes: ' + formatNumber(inventorySummary.totalNotes)">
                {{ formatNumber(inventorySummary.totalNotes) }}
              </div>
              <div class="metric-label">Total Notes</div>
              <div class="metric-description">Physical bank notes</div>
            </div>
            <div class="card-progress">
              <div class="progress-bar" style="width: 70%"></div>
            </div>
          </div>
        </article>

        <!-- Low Stock Alerts Card -->
        <article class="absa-metric-card alerts-card"
                 [class.critical]="inventorySummary.lowStockAlerts.length > 5"
                 tabindex="0" role="button"
                 [attr.aria-label]="'Low stock alerts: ' + inventorySummary.lowStockAlerts.length">
          <div class="card-decoration"></div>
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>warning</mat-icon>
                <div class="alert-pulse" *ngIf="inventorySummary.lowStockAlerts.length > 0"></div>
              </div>
              <div class="metric-trend" [class.warning]="inventorySummary.lowStockAlerts.length > 0">
                <mat-icon>priority_high</mat-icon>
                <span>{{ inventorySummary.lowStockAlerts.length > 0 ? 'Alert' : 'OK' }}</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value">{{ inventorySummary.lowStockAlerts.length }}</div>
              <div class="metric-label">Low Stock Alerts</div>
              <div class="metric-description">Require attention</div>
            </div>
            <div class="card-progress">
              <div class="progress-bar warning" [style.width.%]="inventorySummary.lowStockAlerts.length * 20"></div>
            </div>
          </div>
        </article>

        <!-- Series Count Card -->
        <article class="absa-metric-card series-card" tabindex="0" role="button" aria-label="Available note series">
          <div class="card-decoration"></div>
          <div class="card-content">
            <header class="card-header">
              <div class="metric-icon">
                <mat-icon>category</mat-icon>
              </div>
              <div class="metric-trend positive">
                <mat-icon>check_circle</mat-icon>
                <span>Active</span>
              </div>
            </header>
            <div class="card-body">
              <div class="metric-value">4</div>
              <div class="metric-label">Note Series</div>
              <div class="metric-description">Available types</div>
            </div>
            <div class="card-progress">
              <div class="progress-bar" style="width: 100%"></div>
            </div>
          </div>
        </article>
      </div>
    </section>

    <!-- Detailed Inventory Section -->
    <section class="detailed-inventory-section">
      <!-- Section Header -->
      <div class="section-header">
        <h2 class="section-title">Detailed Inventory</h2>
        <button mat-raised-button class="manage-btn">
          <mat-icon>settings</mat-icon>
          Manage cash inventory by note series
        </button>
      </div>

      <!-- Series Tabs -->
      <div class="series-tabs-container">
        <mat-tab-group class="series-tabs" animationDuration="300ms" (selectedTabChange)="onTabChange($event)">
          <mat-tab *ngFor="let series of seriesData; let i = index" [label]="series.name">
            <ng-template mat-tab-label>
              <span class="tab-label">{{ series.name }}</span>
            </ng-template>

            <!-- Tab Content -->
            <div class="tab-content">
              <!-- Series Summary Cards -->
              <div class="series-summary">
                <div class="summary-card batches-card">
                  <div class="summary-icon">
                    <mat-icon>inventory_2</mat-icon>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ series.totalBatches }} batches + {{ series.totalSingles }} singles</div>
                    <div class="summary-label">Total Notes</div>
                  </div>
                </div>

                <div class="summary-card value-card">
                  <div class="summary-icon">
                    <mat-icon>account_balance_wallet</mat-icon>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ formatCurrency(series.totalValue) }}</div>
                    <div class="summary-label">Total Value</div>
                  </div>
                </div>
              </div>

              <!-- Denomination Cards Grid -->
              <div class="denominations-grid">
                <div class="denomination-card"
                     *ngFor="let denomination of series.denominations; trackBy: trackByDenomination"
                     [class.low-stock]="denomination.stockLevel < 50 && denomination.stockLevel > 0"
                     [class.out-of-stock]="denomination.stockLevel === 0">

                  <!-- Card Header -->
                  <div class="card-header">
                    <div class="denomination-info">
                      <div class="denomination-icon">
                        <mat-icon>payments</mat-icon>
                      </div>
                      <div class="denomination-details">
                        <h3 class="denomination-title">R{{ denomination.value }}</h3>
                        <p class="denomination-series">{{ series.name }}</p>
                        <div class="denomination-status">
                          <span class="status-badge" [class]="getStatusClass(denomination.stockLevel)">
                            {{ getStatusText(denomination.stockLevel) }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Card Body -->
                  <div class="card-body">
                    <div class="inventory-stats">
                      <div class="stat-item">
                        <div class="stat-value">{{ denomination.batches }} batches + {{ denomination.singles }} singles</div>
                        <div class="stat-label">Quantity</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value">{{ formatCurrency(denomination.totalValue) }}</div>
                        <div class="stat-label">Total Value</div>
                      </div>
                    </div>

                    <!-- Stock Level Progress -->
                    <div class="stock-level">
                      <div class="stock-header">
                        <span class="stock-label">Stock Level: {{ denomination.stockLevel }}%</span>
                      </div>
                      <mat-progress-bar
                        [value]="denomination.stockLevel"
                        [color]="denomination.stockLevel === 0 ? 'warn' : denomination.stockLevel < 50 ? 'warn' : 'primary'"
                        mode="determinate"
                        class="stock-progress"
                        [class.out-of-stock-progress]="denomination.stockLevel === 0">
                      </mat-progress-bar>
                    </div>
                  </div>

                  <!-- Card Actions -->
                  <div class="card-actions">
                    <button mat-raised-button
                            color="primary"
                            class="add-cash-btn"
                            (click)="onAddCash(series.id, denomination.value)">
                      <mat-icon>add_circle</mat-icon>
                      Add Cash
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Coin Inventory Tab -->
          <mat-tab label="Coins">
            <ng-template mat-tab-label>
              <span class="tab-label">
                <mat-icon>monetization_on</mat-icon>
                Coins
              </span>
            </ng-template>

            <!-- Coin Tab Content -->
            <div class="tab-content">
              <!-- Coin Summary Cards -->
              <div class="series-summary">
                <div class="summary-card batches-card">
                  <div class="summary-icon">
                    <mat-icon>account_balance_wallet</mat-icon>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ formatCurrency(getTotalCoinValue()) }}</div>
                    <div class="summary-label">Total Coin Value</div>
                  </div>
                </div>

                <div class="summary-card singles-card">
                  <div class="summary-icon">
                    <mat-icon>monetization_on</mat-icon>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ formatNumber(getTotalCoins()) }}</div>
                    <div class="summary-label">Total Coins</div>
                  </div>
                </div>

                <div class="summary-card value-card">
                  <div class="summary-icon">
                    <mat-icon>warning</mat-icon>
                  </div>
                  <div class="summary-content">
                    <div class="summary-value">{{ getLowStockCoins().length }}</div>
                    <div class="summary-label">Low Stock Alerts</div>
                  </div>
                </div>
              </div>

              <!-- Coin Denominations Grid -->
              <div class="coin-denominations-grid">
                <div class="coin-card"
                     *ngFor="let coin of coinInventory; trackBy: trackByItemId"
                     [attr.data-denomination]="coin.denomination"
                     [class.low-stock]="getCoinStockLevel(coin) === 'low'"
                     [class.out-of-stock]="getCoinStockLevel(coin) === 'out-of-stock'">

                  <!-- Coin Header -->
                  <div class="coin-header">
                    <div class="coin-badge" [attr.data-denomination]="coin.denomination">
                      <div class="coin-icon-wrapper">
                        <mat-icon class="coin-icon">monetization_on</mat-icon>
                      </div>
                      <div class="coin-value">{{ getCoinLabel(coin.denomination) }}</div>
                    </div>
                    <div class="stock-indicator" [class]="'status-' + getCoinStockLevel(coin)">
                      <mat-icon>{{ getCoinStockLevel(coin) === 'normal' ? 'check_circle' :
                                   getCoinStockLevel(coin) === 'low' ? 'warning' : 'error' }}</mat-icon>
                      <span class="status-text">{{ getCoinStockLevel(coin) === 'normal' ? 'In Stock' :
                               getCoinStockLevel(coin) === 'low' ? 'Low Stock' : 'Out of Stock' }}</span>
                    </div>
                  </div>

                  <!-- Coin Stats -->
                  <div class="coin-stats">
                    <div class="stat-row">
                      <div class="stat-item primary">
                        <mat-icon>inventory</mat-icon>
                        <span class="stat-value">{{ coin.batches }}</span>
                        <span class="stat-label">Batches</span>
                      </div>
                      <div class="stat-item secondary">
                        <mat-icon>looks_one</mat-icon>
                        <span class="stat-value">{{ formatNumber(coin.quantity) }}</span>
                        <span class="stat-label">Total Coins</span>
                      </div>
                    </div>
                    <div class="total-value">
                      <mat-icon>attach_money</mat-icon>
                      <span class="value-amount">{{ formatCurrency(coin.value) }}</span>
                      <span class="value-label">Total Value</span>
                    </div>
                  </div>

                  <!-- Add Coins Button -->
                  <div class="coin-actions">
                    <button mat-raised-button
                            color="primary"
                            class="add-coins-btn"
                            (click)="openAddCoinModal(coin.denomination)"
                            [attr.aria-label]="'Add ' + getCoinLabel(coin.denomination) + ' coins'">
                      <mat-icon>add_circle</mat-icon>
                      Add Coins
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </div>
    </section>
  </main>

  <!-- Enhanced Floating Action Button -->
  <div class="absa-fab-container">
    <button mat-fab
            class="absa-fab-primary"
            (click)="onAddCash()"
            matTooltip="Add Notes to Inventory"
            matTooltipPosition="left"
            [attr.aria-label]="'Add notes to inventory'">
      <mat-icon>note_add</mat-icon>
    </button>
  </div>
</div>
