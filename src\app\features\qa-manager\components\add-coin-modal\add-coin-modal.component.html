<div class="modal-container">
  <!-- Header -->
  <div class="modal-header">
    <div class="header-content">
      <div class="denomination-badge coin-badge">
        <span class="currency-value">{{ getDenominationLabel() }}</span>
        <div class="coin-icon">
          <mat-icon>monetization_on</mat-icon>
        </div>
      </div>
      <div class="header-info">
        <h2 class="modal-title">{{ getDenominationLabel() }} Coins</h2>
        <p class="current-stock">
          <mat-icon>inventory_2</mat-icon>
          {{ data.currentQuantity }} coins ({{ data.currentBatches }} batches) in stock
        </p>
        <p class="batch-info">
          <mat-icon>info</mat-icon>
          1 batch = {{ getCoinsPerBatch() }} coins = {{ formatCurrency(getBatchValue()) }}
        </p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Content -->
  <div mat-dialog-content class="modal-content">
    <form [formGroup]="addCoinForm" (ngSubmit)="onSubmit()">
      
      <!-- Action Selection -->
      <div class="form-section">
        <div class="section-header">
          <mat-icon class="section-icon">swap_horiz</mat-icon>
          <label class="section-label">Choose Action</label>
        </div>
        <div class="action-cards">
          <div class="action-card" 
               [class.selected]="addCoinForm.get('action')?.value === 'add'"
               (click)="selectAction('add')">
            <div class="card-icon add-icon">
              <mat-icon>add_circle</mat-icon>
            </div>
            <div class="card-content">
              <h4>Add Coins</h4>
              <p>Increase inventory</p>
            </div>
          </div>
          
          <div class="action-card" 
               [class.selected]="addCoinForm.get('action')?.value === 'remove'"
               (click)="selectAction('remove')">
            <div class="card-icon remove-icon">
              <mat-icon>remove_circle</mat-icon>
            </div>
            <div class="card-content">
              <h4>Remove Coins</h4>
              <p>Decrease inventory</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quantity Selection -->
      <div class="form-section">
        <div class="section-header">
          <mat-icon class="section-icon">format_list_numbered</mat-icon>
          <label class="section-label">Quantity</label>
        </div>
        
        <!-- Batch/Singles Toggle -->
        <div class="quantity-type-selector">
          <button type="button" 
                  class="type-btn" 
                  [class.active]="quantityType === 'batches'"
                  (click)="setQuantityType('batches')">
            <mat-icon>inventory</mat-icon>
            <span>Batches</span>
            <small>(1 batch = {{ getCoinsPerBatch() }} coins)</small>
          </button>
          <button type="button" 
                  class="type-btn" 
                  [class.active]="quantityType === 'singles'"
                  (click)="setQuantityType('singles')">
            <mat-icon>looks_one</mat-icon>
            <span>Singles</span>
            <small>(Individual coins)</small>
          </button>
        </div>
        
        <!-- Quantity Controls -->
        <div class="quantity-controls">
          <button type="button" 
                  class="quantity-btn decrease" 
                  (click)="adjustQuantity(-1)"
                  [disabled]="getQuantityValue() <= 1">
            <mat-icon>remove</mat-icon>
          </button>
          
          <div class="quantity-display">
            <input class="quantity-input" 
                   [value]="getQuantityValue()"
                   (input)="onQuantityInputChange($event)"
                   type="number"
                   min="1">
            <span class="quantity-label">{{ quantityType }}</span>
          </div>
          
          <button type="button" 
                  class="quantity-btn increase" 
                  (click)="adjustQuantity(1)">
            <mat-icon>add</mat-icon>
          </button>
        </div>
        
        <!-- Quick Select -->
        <div class="quick-select">
          <span class="quick-label">Quick select:</span>
          <button type="button" 
                  *ngFor="let amount of getQuickAmounts()" 
                  class="quick-btn"
                  [class.selected]="getQuantityValue() === amount"
                  (click)="setQuantityValue(amount)">
            {{ amount }}
          </button>
        </div>
        
        <!-- Conversion Display -->
        <div class="conversion-display">
          <div class="conversion-item">
            <span class="conversion-label">Total Coins:</span>
            <span class="conversion-value">{{ getTotalCoins() }}</span>
          </div>
          <div class="conversion-item">
            <span class="conversion-label">Total Batches:</span>
            <span class="conversion-value">{{ getTotalBatches() }}</span>
          </div>
          <div class="conversion-item">
            <span class="conversion-label">Total Value:</span>
            <span class="conversion-value">{{ formatCurrency(getTotalCoins() * data.denomination) }}</span>
          </div>
        </div>
      </div>

      <!-- Live Summary -->
      <div class="form-section">
        <div class="section-header">
          <mat-icon class="section-icon">analytics</mat-icon>
          <label class="section-label">Live Summary</label>
        </div>
        <div class="summary-cards">
          <div class="summary-card current-stock">
            <div class="summary-icon">
              <mat-icon>inventory_2</mat-icon>
            </div>
            <div class="summary-content">
              <span class="summary-label">Current Stock</span>
              <span class="summary-value">{{ data.currentQuantity }} coins</span>
            </div>
          </div>
          
          <div class="summary-card change-amount" 
               [class.adding]="addCoinForm.get('action')?.value === 'add'"
               [class.removing]="addCoinForm.get('action')?.value === 'remove'">
            <div class="summary-icon">
              <mat-icon>{{ addCoinForm.get('action')?.value === 'add' ? 'trending_up' : 'trending_down' }}</mat-icon>
            </div>
            <div class="summary-content">
              <span class="summary-label">{{ addCoinForm.get('action')?.value === 'add' ? 'Adding' : 'Removing' }}</span>
              <span class="summary-value">{{ getTotalCoins() }} coins</span>
            </div>
          </div>
          
          <div class="summary-card new-total">
            <div class="summary-icon">
              <mat-icon>calculate</mat-icon>
            </div>
            <div class="summary-content">
              <span class="summary-label">New Total</span>
              <span class="summary-value">{{ getNewTotalCoins() }} coins</span>
            </div>
          </div>
          
          <div class="summary-card total-value">
            <div class="summary-icon">
              <mat-icon>attach_money</mat-icon>
            </div>
            <div class="summary-content">
              <span class="summary-label">Total Value</span>
              <span class="summary-value currency">{{ formatCurrency(getTotalValue()) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Notes Section -->
      <div class="form-section">
        <div class="section-header">
          <mat-icon class="section-icon">note_add</mat-icon>
          <label class="section-label">Additional Notes <span class="optional">(Optional)</span></label>
        </div>
        <mat-form-field appearance="outline" class="full-width">
          <textarea matInput 
                    formControlName="notes"
                    placeholder="Add any additional notes, reasons, or comments..."
                    rows="3"
                    maxlength="500"></textarea>
          <mat-hint>{{ (addCoinForm.get('notes')?.value || '').length }}/500 characters</mat-hint>
        </mat-form-field>
      </div>

    </form>
  </div>

  <!-- Actions -->
  <div mat-dialog-actions class="modal-actions">
    <button mat-button type="button" (click)="onCancel()" class="cancel-btn">
      <mat-icon>close</mat-icon>
      Cancel
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSubmit()"
            [disabled]="!addCoinForm.valid || isSubmitting"
            class="submit-btn">
      <mat-icon>{{ addCoinForm.get('action')?.value === 'add' ? 'add_circle' : 'remove_circle' }}</mat-icon>
      {{ addCoinForm.get('action')?.value === 'add' ? 'Add Coins' : 'Remove Coins' }}
      <mat-spinner *ngIf="isSubmitting" diameter="20" class="btn-spinner"></mat-spinner>
    </button>
  </div>
</div>
