import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS } from '../../../shared/models/inventory.model';

export interface AddCashData {
  series?: NoteSeries;
  denomination?: NoteDenomination;
}

// Mock services for now - these would be replaced with actual services
class MockInventoryService {
  addCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string): boolean {
    console.log('Mock InventoryService.addCash called:', { series, denomination, quantity, reason });
    return true; // Always return success for demo
  }
}

class MockSystemLogService {
  logManagerAction(action: string, description: string): void {
    console.log('Mock SystemLogService.logManagerAction called:', { action, description });
  }
}

@Component({
  selector: 'app-add-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="add-cash-modal-container">
      <!-- Modern Header with Gradient -->
      <div class="modal-header">
        <div class="header-content">
          <div class="header-icon">
            <mat-icon>account_balance_wallet</mat-icon>
          </div>
          <div class="header-text">
            <h2>Add Cash Inventory</h2>
            <p>Enhance your cash reserves with precision</p>
          </div>
        </div>
        <button mat-icon-button class="close-button" (click)="onCancel()">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <!-- Main Content Area -->
      <div class="modal-content">
        <form #addCashForm="ngForm" class="add-cash-form">

          <!-- Step 1: Series Selection -->
          <div class="form-section series-selection">
            <div class="section-header">
              <mat-icon class="step-icon">category</mat-icon>
              <h3>Select Note Series</h3>
              <span class="pre-selected-badge" *ngIf="selectedSeries && data?.series">
                <mat-icon>check_circle</mat-icon>
                Pre-selected
              </span>
            </div>
            <div class="series-grid">
              <div *ngFor="let series of availableSeries"
                   class="series-card"
                   [class.selected]="selectedSeries === series.value"
                   (click)="selectSeries(series.value)">
                <div class="series-icon">
                  <mat-icon>{{ getSeriesIcon(series.value) }}</mat-icon>
                </div>
                <div class="series-info">
                  <h4>{{ series.label }}</h4>
                  <p>{{ getSeriesDescription(series.value) }}</p>
                </div>
                <div class="selection-indicator" *ngIf="selectedSeries === series.value">
                  <mat-icon>check_circle</mat-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Denomination Selection -->
          <div class="form-section denomination-selection" *ngIf="selectedSeries">
            <div class="section-header">
              <mat-icon class="step-icon">payments</mat-icon>
              <h3>Choose Denomination</h3>
              <span class="pre-selected-badge" *ngIf="selectedDenomination && data?.denomination">
                <mat-icon>check_circle</mat-icon>
                Pre-selected
              </span>
            </div>
            <div class="denomination-grid">
              <div *ngFor="let denom of availableDenominations"
                   class="denomination-card"
                   [class.selected]="selectedDenomination === denom.value"
                   (click)="selectDenomination(denom.value)">
                <div class="denomination-content">
                  <div class="denomination-icon">
                    <mat-icon>payments</mat-icon>
                  </div>
                  <div class="denomination-info">
                    <h4>{{ denom.label }}</h4>
                    <p>{{ getDenominationDescription(denom.value) }}</p>
                  </div>
                </div>
                <div class="selection-indicator" *ngIf="selectedDenomination === denom.value">
                  <mat-icon>check_circle</mat-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 3: Quantity Input -->
          <div class="form-section quantity-section" *ngIf="selectedDenomination">
            <div class="section-header">
              <mat-icon class="step-icon">calculate</mat-icon>
              <h3>Specify Quantity</h3>
            </div>
            <div class="quantity-controls">
              <div class="quantity-card batches-card">
                <div class="card-header">
                  <mat-icon>inventory_2</mat-icon>
                  <h4>Batches</h4>
                  <span class="helper-text">100 notes each</span>
                </div>
                <div class="input-container">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                    <mat-icon>remove</mat-icon>
                  </button>
                  <input type="number"
                         [(ngModel)]="batches"
                         name="batches"
                         min="0"
                         class="quantity-input"
                         (input)="onQuantityChange()">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
                <div class="quantity-display">
                  <span class="notes-count">{{ batches * 100 }} notes</span>
                </div>
              </div>

              <div class="quantity-card singles-card">
                <div class="card-header">
                  <mat-icon>note</mat-icon>
                  <h4>Singles</h4>
                  <span class="helper-text">Individual notes</span>
                </div>
                <div class="input-container">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                    <mat-icon>remove</mat-icon>
                  </button>
                  <input type="number"
                         [(ngModel)]="singles"
                         name="singles"
                         min="0"
                         max="99"
                         class="quantity-input"
                         (input)="onQuantityChange()">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)" [disabled]="singles >= 99">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
                <div class="quantity-display">
                  <span class="notes-count">{{ singles }} notes</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 4: Reason Input -->
          <div class="form-section reason-section" *ngIf="totalQuantity > 0">
            <div class="section-header">
              <mat-icon class="step-icon">description</mat-icon>
              <h3>Add Reason (Optional)</h3>
            </div>
            <mat-form-field appearance="outline" class="reason-field">
              <mat-label>Reason for adding cash (optional)</mat-label>
              <textarea matInput
                        [(ngModel)]="reason"
                        name="reason"
                        rows="3"
                        placeholder="e.g., New stock delivery, Inventory replenishment, etc. (optional)"></textarea>
              <mat-icon matSuffix>edit_note</mat-icon>
            </mat-form-field>
          </div>

          <!-- Live Summary Display -->
          <div class="summary-display" *ngIf="selectedDenomination && totalQuantity > 0">
            <div class="summary-preview">
              <div class="preview-card">
                <div class="preview-header">
                  <div class="preview-icon">
                    <mat-icon>receipt_long</mat-icon>
                  </div>
                  <div class="preview-title">
                    <h4>Adding to Inventory</h4>
                    <p>{{ getSelectedSeriesLabel() }} • {{ getSelectedDenominationLabel() }}</p>
                  </div>
                </div>

                <div class="preview-metrics">
                  <div class="metric-item">
                    <div class="metric-icon">
                      <mat-icon>inventory_2</mat-icon>
                    </div>
                    <div class="metric-details">
                      <span class="metric-value">{{ totalQuantity }}</span>
                      <span class="metric-label">Notes</span>
                    </div>
                  </div>

                  <div class="metric-divider"></div>

                  <div class="metric-item total-value">
                    <div class="metric-icon">
                      <mat-icon>account_balance_wallet</mat-icon>
                    </div>
                    <div class="metric-details">
                      <span class="metric-value">{{ formatCurrency(selectedDenomination * totalQuantity) }}</span>
                      <span class="metric-label">Total Value</span>
                    </div>
                  </div>
                </div>

                <div class="breakdown-section" *ngIf="batches > 0 || singles > 0">
                  <div class="breakdown-title">
                    <mat-icon>analytics</mat-icon>
                    <span>Breakdown</span>
                  </div>
                  <div class="breakdown-items">
                    <div class="breakdown-item" *ngIf="batches > 0">
                      <span class="breakdown-label">{{ batches }} batch{{ batches !== 1 ? 'es' : '' }}</span>
                      <span class="breakdown-value">{{ batches * 100 }} notes</span>
                    </div>
                    <div class="breakdown-item" *ngIf="singles > 0">
                      <span class="breakdown-label">{{ singles }} single{{ singles !== 1 ? 's' : '' }}</span>
                      <span class="breakdown-value">{{ singles }} notes</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Action Buttons -->
      <div class="modal-actions">
        <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
          <mat-icon>cancel</mat-icon>
          Cancel
        </button>
        <button mat-raised-button
                class="add-btn"
                [disabled]="!isFormValid()"
                (click)="onAddCash()">
          <mat-icon>add_circle</mat-icon>
          Add to Inventory
        </button>
      </div>
    </div>
  `,
  styles: [`
    // Modern Container with Better Colors
    .add-cash-modal-container {
      width: 900px;
      max-width: 95vw;
      max-height: 90vh;
      background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.95) 0%,
        rgba(241, 245, 249, 0.98) 50%,
        rgba(236, 242, 248, 0.99) 100%);
      backdrop-filter: blur(24px);
      -webkit-backdrop-filter: blur(24px);
      border-radius: 24px;
      box-shadow:
        0 32px 64px rgba(15, 23, 42, 0.15),
        0 16px 32px rgba(15, 23, 42, 0.10),
        0 8px 16px rgba(15, 23, 42, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
      border: 1px solid rgba(226, 232, 240, 0.8);
      overflow: hidden;
      position: relative;
    }

    // Gradient Header
    .modal-header {
      background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
      padding: 2rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
      }

      .header-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        z-index: 1;
        position: relative;

        .header-icon {
          width: 64px;
          height: 64px;
          background: rgba(255,255,255,0.2);
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255,255,255,0.3);

          mat-icon {
            font-size: 2rem;
            color: white;
          }
        }

        .header-text {
          h2 {
            color: white;
            font-size: 2rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
          }

          p {
            color: rgba(255,255,255,0.9);
            margin: 0.5rem 0 0 0;
            font-size: 1rem;
            font-weight: 400;
          }
        }
      }

      .close-button {
        background: rgba(255,255,255,0.2);
        color: white;
        border: 1px solid rgba(255,255,255,0.3);
        backdrop-filter: blur(10px);
        z-index: 1;
        position: relative;

        &:hover {
          background: rgba(255,255,255,0.3);
          transform: scale(1.05);
        }
      }
    }

    // Main Content Area
    .modal-content {
      padding: 2rem;
      max-height: 60vh;
      overflow-y: auto;
      scrollbar-width: thin;
      scrollbar-color: rgba(148, 163, 184, 0.6) transparent;
      background: rgba(255, 255, 255, 0.4);

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(148, 163, 184, 0.6);
        border-radius: 3px;

        &:hover {
          background: rgba(148, 163, 184, 0.8);
        }
      }
    }

    // Form Sections
    .form-section {
      margin-bottom: 2.5rem;
      animation: slideInUp 0.6s ease-out;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;

        .step-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
          color: white;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.2rem;
          box-shadow: 0 4px 12px rgba(227, 24, 55, 0.3);
        }

        h3 {
          color: var(--absa-dark-blue);
          font-size: 1.4rem;
          font-weight: 600;
          margin: 0;
        }

        .pre-selected-badge {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: linear-gradient(135deg, var(--absa-green) 0%, #10B981 100%);
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 600;
          margin-left: auto;
          box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);

          mat-icon {
            font-size: 1rem;
            width: 1rem;
            height: 1rem;
          }
        }
      }
    }

    // Series Selection Grid
    .series-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      .series-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(226, 232, 240, 0.6);
        border-radius: 16px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 24px rgba(0,0,0,0.15);
          border-color: var(--absa-red);

          &::before {
            opacity: 0.05;
          }
        }

        &.selected {
          border-color: var(--absa-red);
          background: linear-gradient(135deg, rgba(227, 24, 55, 0.05) 0%, rgba(185, 28, 60, 0.05) 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(227, 24, 55, 0.2);

          &::before {
            opacity: 0.1;
          }
        }

        .series-icon {
          width: 48px;
          height: 48px;
          background: linear-gradient(135deg, var(--absa-light-blue) 0%, var(--absa-dark-blue) 100%);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1rem;
          position: relative;
          z-index: 1;

          mat-icon {
            color: white;
            font-size: 1.5rem;
          }
        }

        .series-info {
          position: relative;
          z-index: 1;

          h4 {
            color: var(--absa-dark-blue);
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0 0 0.5rem 0;
          }

          p {
            color: var(--absa-gray-medium);
            font-size: 0.9rem;
            margin: 0;
            line-height: 1.4;
          }
        }

        .selection-indicator {
          position: absolute;
          top: 1rem;
          right: 1rem;
          z-index: 2;

          mat-icon {
            color: var(--absa-green);
            font-size: 1.5rem;
            animation: checkmark 0.3s ease-out;
          }
        }
      }
    }

    // Denomination Selection Grid
    .denomination-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      .denomination-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(226, 232, 240, 0.6);
        border-radius: 16px;
        padding: 1.5rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, var(--absa-gold) 0%, #FFB81C 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 24px rgba(0,0,0,0.15);
          border-color: var(--absa-gold);

          &::before {
            opacity: 0.05;
          }
        }

        &.selected {
          border-color: var(--absa-red);
          background: linear-gradient(135deg, rgba(227, 24, 55, 0.05) 0%, rgba(185, 28, 60, 0.05) 100%);
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(227, 24, 55, 0.2);

          &::before {
            opacity: 0.1;
          }
        }

        .denomination-content {
          display: flex;
          align-items: center;
          gap: 1rem;
          position: relative;
          z-index: 1;

          .denomination-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--absa-gold) 0%, #FFB81C 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;

            mat-icon {
              color: white;
              font-size: 1.5rem;
            }
          }

          .denomination-info {
            flex: 1;

            h4 {
              color: var(--absa-dark-blue);
              font-size: 1.2rem;
              font-weight: 600;
              margin: 0 0 0.25rem 0;
            }

            p {
              color: var(--absa-gray-medium);
              font-size: 0.9rem;
              margin: 0;
              line-height: 1.4;
            }
          }
        }

        .selection-indicator {
          position: absolute;
          top: 1rem;
          right: 1rem;
          z-index: 2;

          mat-icon {
            color: var(--absa-green);
            font-size: 1.5rem;
            animation: checkmark 0.3s ease-out;
          }
        }
      }
    }

    // Quantity Controls
    .quantity-controls {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;

      .quantity-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(226, 232, 240, 0.6);
        border-radius: 16px;
        padding: 1.5rem;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--absa-light-blue);
          box-shadow: 0 4px 12px rgba(0,102,204,0.1);
          background: rgba(255, 255, 255, 0.9);
        }

        .card-header {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 1rem;

          mat-icon {
            color: var(--absa-light-blue);
            font-size: 1.5rem;
          }

          h4 {
            color: var(--absa-dark-blue);
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
          }

          .helper-text {
            color: var(--absa-gray-medium);
            font-size: 0.8rem;
            margin-left: auto;
          }
        }

        .input-container {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 1rem;

          .quantity-btn {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background: var(--absa-gray-light);
            border: 1px solid #E5E7EB;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            min-width: 36px;
            line-height: 1;

            &:hover:not(:disabled) {
              background: var(--absa-light-blue);
              color: white;
              transform: scale(1.05);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            mat-icon {
              font-size: 1.2rem;
              width: 1.2rem;
              height: 1.2rem;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .quantity-input {
            flex: 1;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            padding: 0.75rem;
            background: #F9FAFB;
            color: var(--absa-dark-blue);
            transition: all 0.2s ease;

            &:focus {
              outline: none;
              border-color: var(--absa-red);
              background: white;
              box-shadow: 0 0 0 3px rgba(227, 24, 55, 0.1);
            }
          }
        }

        .quantity-display {
          text-align: center;
          padding: 0.5rem;
          background: linear-gradient(135deg, var(--absa-gray-light) 0%, #F3F4F6 100%);
          border-radius: 8px;

          .notes-count {
            color: var(--absa-dark-blue);
            font-weight: 600;
            font-size: 0.9rem;
          }
        }
      }
    }

    // Reason Section
    .reason-section {
      .reason-field {
        width: 100%;

        mat-label {
          color: var(--absa-dark-blue);
          font-weight: 500;
        }

        textarea {
          resize: vertical;
          min-height: 80px;
          font-family: inherit;
        }

        .mat-form-field-outline {
          color: #E5E7EB;
        }

        &.mat-focused .mat-form-field-outline-thick {
          color: var(--absa-red);
        }
      }
    }

    // Modern Summary Display
    .summary-display {
      margin-top: 1.5rem;

      .summary-preview {
        .preview-card {
          background: linear-gradient(135deg,
            rgba(255,255,255,0.9) 0%,
            rgba(248,250,252,0.95) 100%);
          border: 1px solid rgba(148, 163, 184, 0.2);
          border-radius: 20px;
          padding: 1.5rem;
          box-shadow:
            0 8px 32px rgba(15, 23, 42, 0.08),
            0 4px 16px rgba(15, 23, 42, 0.04);
          backdrop-filter: blur(15px);
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
              var(--absa-red) 0%,
              var(--absa-light-blue) 50%,
              var(--absa-gold) 100%);
          }

          .preview-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;

            .preview-icon {
              width: 48px;
              height: 48px;
              background: linear-gradient(135deg, var(--absa-light-blue), var(--absa-dark-blue));
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 4px 12px rgba(0,102,204,0.2);

              mat-icon {
                color: white;
                font-size: 1.5rem;
              }
            }

            .preview-title {
              h4 {
                margin: 0 0 0.25rem 0;
                font-size: 1.1rem;
                font-weight: 600;
                color: var(--absa-dark-blue);
              }

              p {
                margin: 0;
                font-size: 0.9rem;
                color: var(--absa-gray-medium);
                font-weight: 500;
              }
            }
          }

          .preview-metrics {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;

            .metric-item {
              display: flex;
              align-items: center;
              gap: 0.75rem;
              flex: 1;

              .metric-icon {
                width: 40px;
                height: 40px;
                background: linear-gradient(135deg, var(--absa-gray-light), #f8fafc);
                border: 1px solid rgba(0,102,204,0.1);
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;

                mat-icon {
                  font-size: 1.25rem;
                  color: var(--absa-light-blue);
                }
              }

              .metric-details {
                display: flex;
                flex-direction: column;

                .metric-value {
                  font-size: 1.25rem;
                  font-weight: 700;
                  color: var(--absa-dark-blue);
                  line-height: 1.2;
                }

                .metric-label {
                  font-size: 0.8rem;
                  color: var(--absa-gray-medium);
                  font-weight: 500;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                }
              }

              &.total-value {
                .metric-icon {
                  background: linear-gradient(135deg, var(--absa-gold), #FFB81C);

                  mat-icon {
                    color: white;
                  }
                }

                .metric-value {
                  color: var(--absa-red);
                  font-size: 1.4rem;
                }
              }
            }

            .metric-divider {
              width: 1px;
              height: 40px;
              background: linear-gradient(to bottom,
                transparent,
                rgba(0,102,204,0.2),
                transparent);
            }
          }

          .breakdown-section {
            border-top: 1px solid rgba(0,102,204,0.1);
            padding-top: 1rem;

            .breakdown-title {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              margin-bottom: 0.75rem;

              mat-icon {
                font-size: 1rem;
                color: var(--absa-light-blue);
              }

              span {
                font-size: 0.9rem;
                font-weight: 600;
                color: var(--absa-dark-blue);
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
            }

            .breakdown-items {
              display: flex;
              flex-direction: column;
              gap: 0.5rem;

              .breakdown-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0.75rem;
                background: rgba(0,102,204,0.03);
                border-radius: 8px;
                border-left: 3px solid var(--absa-light-blue);

                .breakdown-label {
                  font-size: 0.85rem;
                  color: var(--absa-gray-medium);
                  font-weight: 500;
                }

                .breakdown-value {
                  font-size: 0.85rem;
                  font-weight: 600;
                  color: var(--absa-dark-blue);
                }
              }
            }
          }
        }
      }
    }

    // Modal Actions
    .modal-actions {
      padding: 1.5rem 2rem;
      background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.9) 0%,
        rgba(255, 255, 255, 0.8) 100%);
      backdrop-filter: blur(10px);
      border-top: 1px solid rgba(226, 232, 240, 0.6);
      display: flex;
      justify-content: flex-end;
      gap: 1rem;

      .cancel-btn {
        border: 2px solid var(--absa-gray-medium);
        color: var(--absa-gray-dark);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--absa-red);
          color: var(--absa-red);
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(227, 24, 55, 0.2);
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }

      .add-btn {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(227, 24, 55, 0.3);

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(227, 24, 55, 0.4);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }

    // Animations
    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes checkmark {
      0% {
        transform: scale(0);
        opacity: 0;
      }
      50% {
        transform: scale(1.2);
        opacity: 1;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    // Responsive Design
    @media (max-width: 768px) {
      .add-cash-modal-container {
        width: 95vw;
        max-height: 95vh;
      }

      .modal-header {
        padding: 1.5rem;

        .header-content {
          gap: 1rem;

          .header-icon {
            width: 48px;
            height: 48px;

            mat-icon {
              font-size: 1.5rem;
            }
          }

          .header-text h2 {
            font-size: 1.5rem;
          }
        }
      }

      .modal-content {
        padding: 1.5rem;
      }

      .series-grid {
        grid-template-columns: 1fr;
      }

      .denomination-grid {
        grid-template-columns: 1fr;
      }

      .quantity-controls {
        grid-template-columns: 1fr;
      }

      .modal-actions {
        padding: 1rem 1.5rem;
        flex-direction: column;

        .cancel-btn,
        .add-btn {
          width: 100%;
          justify-content: center;
        }
      }
    }

    // Global Modal Styles
    :host ::ng-deep {
      .add-cash-modal-backdrop {
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        background: rgba(15, 23, 42, 0.75) !important;
        animation: fadeIn 0.4s ease-out;
      }

      .add-cash-modal-panel {
        border-radius: 24px !important;
        overflow: hidden !important;
        box-shadow: 0 32px 64px rgba(0,0,0,0.12) !important;

        .mat-mdc-dialog-container {
          border-radius: 24px !important;
          overflow: hidden !important;
          padding: 0 !important;
        }
      }

      .modern-modal-panel {
        .mat-mdc-dialog-container {
          max-width: none !important;
          max-height: none !important;
        }
      }
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  `]
})
export class AddCashModalComponent {
  selectedSeries: NoteSeries | null = null;
  selectedDenomination: NoteDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Computed property for total quantity
  get totalQuantity(): number {
    return (this.batches * 100) + this.singles;
  }

  availableSeries = Object.values(NoteSeries).map(series => ({
    value: series,
    label: NOTE_SERIES_LABELS[series]
  }));

  availableDenominations = Object.values(NoteDenomination)
    .filter(d => typeof d === 'number')
    .map(denom => ({
      value: denom as NoteDenomination,
      label: DENOMINATION_LABELS[denom as NoteDenomination]
    }));

  private inventoryService = new MockInventoryService();
  private systemLogService = new MockSystemLogService();

  constructor(
    private dialogRef: MatDialogRef<AddCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCashData,
    private snackBar: MatSnackBar
  ) {
    // Pre-populate if data provided
    if (data?.series) {
      this.selectedSeries = data.series;
    }
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
  }

  isFormValid(): boolean {
    return !!(this.selectedSeries &&
              this.selectedDenomination &&
              this.totalQuantity > 0);
  }

  onAddCash(): void {
    if (!this.isFormValid()) {
      this.snackBar.open('Please fill in all required fields', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory addition"
      const reasonText = this.reason.trim() || 'Manual inventory addition';

      const success = this.inventoryService.addCash(
        this.selectedSeries!,
        this.selectedDenomination!,
        this.totalQuantity,
        reasonText
      );

      if (success) {
        // Create descriptive message for logging
        const batchDescription = this.batches > 0 ? `${this.batches} batch${this.batches !== 1 ? 'es' : ''}` : '';
        const singlesDescription = this.singles > 0 ? `${this.singles} single${this.singles !== 1 ? 's' : ''}` : '';
        const quantityDescription = [batchDescription, singlesDescription].filter(Boolean).join(' + ');

        // Log the action
        this.systemLogService.logManagerAction(
          'Add Cash Inventory',
          `Added ${quantityDescription} (${this.totalQuantity} total) x ${DENOMINATION_LABELS[this.selectedDenomination!]} (${NOTE_SERIES_LABELS[this.selectedSeries!]}) - ${this.reason}`
        );

        this.snackBar.open(
          `Successfully added ${quantityDescription} (${this.totalQuantity} notes) x ${DENOMINATION_LABELS[this.selectedDenomination!]}`,
          'Close',
          { duration: 4000 }
        );

        this.dialogRef.close({ success: true, added: this.totalQuantity });
      } else {
        this.snackBar.open('Failed to add cash inventory', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error adding cash:', error);
      this.snackBar.open('Error adding cash inventory', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  // New methods for enhanced UI functionality
  selectSeries(series: NoteSeries): void {
    this.selectedSeries = series;
    // Reset denomination when series changes
    this.selectedDenomination = null;
  }

  selectDenomination(denomination: NoteDenomination): void {
    this.selectedDenomination = denomination;
  }

  adjustBatches(delta: number): void {
    const newValue = this.batches + delta;
    if (newValue >= 0) {
      this.batches = newValue;
      this.onQuantityChange();
    }
  }

  adjustSingles(delta: number): void {
    const newValue = this.singles + delta;
    if (newValue >= 0 && newValue <= 99) {
      this.singles = newValue;
      this.onQuantityChange();
    }
  }

  onQuantityChange(): void {
    // Trigger change detection for real-time updates
    // This method can be extended for additional validation or calculations
  }

  getSeriesIcon(series: NoteSeries): string {
    const iconMap: { [key in NoteSeries]: string } = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases'
    };
    return iconMap[series] || 'category';
  }

  getSeriesDescription(series: NoteSeries): string {
    const descriptionMap: { [key in NoteSeries]: string } = {
      [NoteSeries.MANDELA]: 'Modern South African currency featuring Nelson Mandela',
      [NoteSeries.BIG_5]: 'Wildlife series featuring the Big 5 animals',
      [NoteSeries.COMMEMORATIVE]: 'Special edition commemorative notes',
      [NoteSeries.V6]: 'Latest generation security features'
    };
    return descriptionMap[series] || 'Standard currency series';
  }

  getDenominationDescription(denomination: NoteDenomination): string {
    const descriptionMap: { [key in NoteDenomination]: string } = {
      [NoteDenomination.R10]: 'Ten Rand note - Lowest denomination',
      [NoteDenomination.R20]: 'Twenty Rand note - Common denomination',
      [NoteDenomination.R50]: 'Fifty Rand note - Medium denomination',
      [NoteDenomination.R100]: 'One Hundred Rand note - High denomination',
      [NoteDenomination.R200]: 'Two Hundred Rand note - Highest denomination'
    };
    return descriptionMap[denomination] || 'Standard currency note';
  }

  getSelectedSeriesLabel(): string {
    if (!this.selectedSeries) return '';
    return NOTE_SERIES_LABELS[this.selectedSeries];
  }

  getSelectedDenominationLabel(): string {
    if (!this.selectedDenomination) return '';
    return DENOMINATION_LABELS[this.selectedDenomination];
  }
}
